import 'dart:math' as math;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart' hide NavigationDestination;
import 'package:dasso_reader/config/navigation_system.dart';
import 'package:dasso_reader/config/design_system.dart';
import 'package:dasso_reader/config/responsive_system.dart';
import 'package:dasso_reader/config/app_icons.dart';
import 'package:dasso_reader/config/adaptive_icons.dart';
import 'package:dasso_reader/config/platform_adaptations.dart';
import 'package:dasso_reader/utils/performance/tablet_performance_utils.dart';
import 'package:dasso_reader/utils/state_management/rebuild_optimization.dart';
import 'package:dasso_reader/utils/log/common.dart';
import 'package:flutter/material.dart' as material;

/// Lazy loading manager for NavigationRail destinations
class NavigationRailLazyLoader {
  static final Map<String, NavigationRailDestination> _destinationCache = {};
  static final Map<String, bool> _loadingStates = {};

  /// Get or create a NavigationRail destination with lazy loading
  static NavigationRailDestination getDestination(
    String key,
    NavigationDestination destination,
    BuildContext context, {
    bool forceReload = false,
  }) {
    // Return cached destination if available and not forcing reload
    if (!forceReload && _destinationCache.containsKey(key)) {
      return _destinationCache[key]!;
    }

    // Check if already loading
    if (_loadingStates[key] == true) {
      return _createPlaceholderDestination(context);
    }

    // Mark as loading and create destination
    _loadingStates[key] = true;

    final railDestination = NavigationRailDestination(
      icon: Icon(destination.icon),
      selectedIcon: Icon(destination.getIcon(selected: true)),
      label: Text(destination.getLabel(context)),
    );

    // Cache the destination
    _destinationCache[key] = railDestination;
    _loadingStates[key] = false;

    return railDestination;
  }

  /// Create a placeholder destination for loading state
  static NavigationRailDestination _createPlaceholderDestination(
    BuildContext context,
  ) {
    return NavigationRailDestination(
      icon: Container(
        width: AppIcons.sizeM,
        height: AppIcons.sizeM,
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surfaceContainerHighest,
          borderRadius: BorderRadius.circular(DesignSystem.radiusS),
        ),
      ),
      label: Container(
        width: 60,
        height: 12,
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surfaceContainerHighest,
          borderRadius: BorderRadius.circular(DesignSystem.radiusS),
        ),
      ),
    );
  }

  /// Clear cache for specific destination
  static void clearDestination(String key) {
    _destinationCache.remove(key);
    _loadingStates.remove(key);
  }

  /// Clear all cached destinations
  static void clearAll() {
    _destinationCache.clear();
    _loadingStates.clear();
  }

  /// Get cache statistics for performance monitoring
  static Map<String, dynamic> getCacheStats() {
    return {
      'cachedDestinations': _destinationCache.length,
      'loadingDestinations':
          _loadingStates.values.where((loading) => loading).length,
      'cacheKeys': _destinationCache.keys.toList(),
    };
  }
}

/// Optimized search bar animation controller for NavigationRail
class SearchBarAnimationController extends ChangeNotifier {
  late AnimationController _animationController;
  late Animation<double> _expandAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  bool _isExpanded = false;
  bool _isAnimating = false;

  SearchBarAnimationController({required TickerProvider vsync}) {
    _animationController = AnimationController(
      duration: DesignSystem.durationMedium,
      vsync: vsync,
    );

    // Optimized curves for smooth tablet animations
    _expandAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeOutCubic, // Smooth expansion
        reverseCurve: Curves.easeInCubic, // Quick collapse
      ),
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.3, 1.0, curve: Curves.easeOut),
        reverseCurve: const Interval(0.0, 0.7, curve: Curves.easeIn),
      ),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(-0.2, 0.0),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeOutBack,
        reverseCurve: Curves.easeInBack,
      ),
    );

    // Listen for animation status changes
    _animationController.addStatusListener(_onAnimationStatusChanged);
  }

  // Getters
  bool get isExpanded => _isExpanded;
  bool get isAnimating => _isAnimating;
  Animation<double> get expandAnimation => _expandAnimation;
  Animation<double> get fadeAnimation => _fadeAnimation;
  Animation<Offset> get slideAnimation => _slideAnimation;

  /// Expand search bar with optimized animation
  Future<void> expand() async {
    if (_isExpanded || _isAnimating) return;

    _isAnimating = true;
    notifyListeners();

    // Start performance tracking
    TabletPerformanceUtils.instance.startSearchBarAnimationProfiling(
      'navigation_rail_search',
      'expand',
    );

    try {
      await _animationController.forward();
      _isExpanded = true;
    } catch (e) {
      AnxLog.warning('Search bar expand animation failed: $e');
    } finally {
      _isAnimating = false;
      notifyListeners();

      // End performance tracking
      TabletPerformanceUtils.instance.endSearchBarAnimationProfiling(
        'navigation_rail_search',
        'expand',
        wasSmooth: _animationController.status == AnimationStatus.completed,
      );
    }
  }

  /// Collapse search bar with optimized animation
  Future<void> collapse() async {
    if (!_isExpanded || _isAnimating) return;

    _isAnimating = true;
    notifyListeners();

    // Start performance tracking
    TabletPerformanceUtils.instance.startSearchBarAnimationProfiling(
      'navigation_rail_search',
      'collapse',
    );

    try {
      await _animationController.reverse();
      _isExpanded = false;
    } catch (e) {
      AnxLog.warning('Search bar collapse animation failed: $e');
    } finally {
      _isAnimating = false;
      notifyListeners();

      // End performance tracking
      TabletPerformanceUtils.instance.endSearchBarAnimationProfiling(
        'navigation_rail_search',
        'collapse',
        wasSmooth: _animationController.status == AnimationStatus.dismissed,
      );
    }
  }

  /// Toggle search bar state
  Future<void> toggle() async {
    if (_isExpanded) {
      await collapse();
    } else {
      await expand();
    }
  }

  void _onAnimationStatusChanged(AnimationStatus status) {
    // Track animation performance impact on NavigationRail
    if (status == AnimationStatus.completed ||
        status == AnimationStatus.dismissed) {
      TabletPerformanceUtils.instance.trackSearchBarImpactOnNavigationRail(
        'enhanced_navigation_rail',
        searchBarVisible: _isExpanded,
        isAnimating: false,
        lastAnimationDuration: DesignSystem.durationMedium,
      );
    }
  }

  @override
  void dispose() {
    _animationController.removeStatusListener(_onAnimationStatusChanged);
    _animationController.dispose();
    super.dispose();
  }
}

/// Intelligent caching system for side menu components
class SideMenuComponentCache {
  static final Map<String, Widget> _widgetCache = {};
  static final Map<String, DateTime> _cacheTimestamps = {};
  static final Map<String, int> _accessCounts = {};

  // Cache configuration
  static const Duration _cacheExpiry = Duration(minutes: 30);
  static const int _maxCacheSize = 50;
  static const int _accessThreshold = 3; // Cache widgets accessed 3+ times

  /// Get or create a cached widget
  static Widget getOrCache(
    String key,
    Widget Function() widgetBuilder, {
    bool forceRefresh = false,
    Duration? customExpiry,
  }) {
    final now = DateTime.now();
    final expiry = customExpiry ?? _cacheExpiry;

    // Check if we should use cached version
    if (!forceRefresh && _widgetCache.containsKey(key)) {
      final timestamp = _cacheTimestamps[key];
      if (timestamp != null && now.difference(timestamp) < expiry) {
        // Update access count and return cached widget
        _accessCounts[key] = (_accessCounts[key] ?? 0) + 1;

        if (kDebugMode) {
          AnxLog.info(
            '📦 Cache hit for widget: $key (${_accessCounts[key]} accesses)',
          );
        }

        return _widgetCache[key]!;
      } else {
        // Cache expired, remove it
        _removeFromCache(key);
      }
    }

    // Build new widget
    final widget = widgetBuilder();

    // Decide whether to cache based on access pattern
    final accessCount = _accessCounts[key] ?? 0;
    if (accessCount >= _accessThreshold || forceRefresh) {
      _addToCache(key, widget, now);
    } else {
      // Just increment access count for future caching decision
      _accessCounts[key] = accessCount + 1;
    }

    return widget;
  }

  /// Add widget to cache with size management
  static void _addToCache(String key, Widget widget, DateTime timestamp) {
    // Manage cache size
    if (_widgetCache.length >= _maxCacheSize) {
      _evictLeastUsed();
    }

    _widgetCache[key] = widget;
    _cacheTimestamps[key] = timestamp;

    if (kDebugMode) {
      AnxLog.info(
        '📦 Cached widget: $key (cache size: ${_widgetCache.length})',
      );
    }
  }

  /// Remove widget from cache
  static void _removeFromCache(String key) {
    _widgetCache.remove(key);
    _cacheTimestamps.remove(key);

    if (kDebugMode) {
      AnxLog.info('📦 Removed from cache: $key');
    }
  }

  /// Evict least used widgets when cache is full
  static void _evictLeastUsed() {
    if (_accessCounts.isEmpty) return;

    // Find the least accessed widget
    String? leastUsedKey;
    int minAccess = double.maxFinite.toInt();

    for (final entry in _accessCounts.entries) {
      if (_widgetCache.containsKey(entry.key) && entry.value < minAccess) {
        minAccess = entry.value;
        leastUsedKey = entry.key;
      }
    }

    if (leastUsedKey != null) {
      _removeFromCache(leastUsedKey);
      _accessCounts.remove(leastUsedKey);

      if (kDebugMode) {
        AnxLog.info(
          '📦 Evicted least used widget: $leastUsedKey ($minAccess accesses)',
        );
      }
    }
  }

  /// Clear expired cache entries
  static void clearExpired() {
    final now = DateTime.now();
    final expiredKeys = <String>[];

    for (final entry in _cacheTimestamps.entries) {
      if (now.difference(entry.value) > _cacheExpiry) {
        expiredKeys.add(entry.key);
      }
    }

    for (final key in expiredKeys) {
      _removeFromCache(key);
    }

    if (kDebugMode && expiredKeys.isNotEmpty) {
      AnxLog.info('📦 Cleared ${expiredKeys.length} expired cache entries');
    }
  }

  /// Clear all cache entries
  static void clearAll() {
    final cacheSize = _widgetCache.length;
    _widgetCache.clear();
    _cacheTimestamps.clear();
    _accessCounts.clear();

    if (kDebugMode) {
      AnxLog.info('📦 Cleared all cache entries ($cacheSize widgets)');
    }
  }

  /// Get cache statistics
  static Map<String, dynamic> getStats() {
    final now = DateTime.now();
    int expiredCount = 0;

    for (final timestamp in _cacheTimestamps.values) {
      if (now.difference(timestamp) > _cacheExpiry) {
        expiredCount++;
      }
    }

    return {
      'totalCached': _widgetCache.length,
      'expiredEntries': expiredCount,
      'totalAccesses':
          _accessCounts.values.fold(0, (sum, count) => sum + count),
      'averageAccesses': _accessCounts.isNotEmpty
          ? _accessCounts.values.reduce((a, b) => a + b) / _accessCounts.length
          : 0.0,
      'cacheKeys': _widgetCache.keys.toList(),
    };
  }

  /// Preload commonly used widgets
  static void preloadCommonWidgets(BuildContext context) {
    // Preload common navigation icons
    final commonIcons = [
      'bookshelf_icon',
      'dictionary_icon',
      'vocabulary_icon',
      'hsk_icon',
      'notes_icon',
    ];

    for (final iconKey in commonIcons) {
      getOrCache(
        iconKey,
        () => Icon(
          AdaptiveIcons.bookshelf,
        ), // Placeholder - would be actual icons
        forceRefresh: false,
      );
    }

    if (kDebugMode) {
      AnxLog.info('📦 Preloaded ${commonIcons.length} common widgets');
    }
  }
}

/// RepaintBoundary optimization utilities for NavigationRail components
class NavigationRailRepaintOptimizer {
  /// Wrap widget with RepaintBoundary if it meets optimization criteria
  static Widget optimizeWidget(
    Widget child, {
    required String componentType,
    required bool isExpensive,
    required bool changesFrequently,
  }) {
    // Only wrap with RepaintBoundary if the widget is expensive to render
    // but doesn't change frequently
    if (isExpensive && !changesFrequently) {
      if (kDebugMode) {
        AnxLog.info('🎨 Optimizing $componentType with RepaintBoundary');
      }
      return RepaintBoundary(child: child);
    }

    return child;
  }

  /// Optimize NavigationRail leading component (search bar, avatar, etc.)
  static Widget optimizeLeading(Widget leading) {
    return optimizeWidget(
      leading,
      componentType: 'NavigationRail Leading',
      isExpensive: true, // Search bars and avatars are expensive
      changesFrequently: false, // Leading components don't change often
    );
  }

  /// Optimize NavigationRail trailing component
  static Widget optimizeTrailing(Widget trailing) {
    return optimizeWidget(
      trailing,
      componentType: 'NavigationRail Trailing',
      isExpensive: true, // Trailing components often contain complex widgets
      changesFrequently: false, // Trailing components are usually static
    );
  }

  /// Optimize NavigationRail destination icon
  static Widget optimizeDestinationIcon(
    Widget icon, {
    required bool isSelected,
  }) {
    return optimizeWidget(
      icon,
      componentType: 'Destination Icon',
      isExpensive: true, // Icons can be expensive with custom graphics
      changesFrequently: isSelected, // Selected icons change with navigation
    );
  }

  /// Optimize NavigationRail destination label
  static Widget optimizeDestinationLabel(Widget label) {
    return optimizeWidget(
      label,
      componentType: 'Destination Label',
      isExpensive: false, // Text labels are usually not expensive
      changesFrequently: false, // Labels don't change frequently
    );
  }

  /// Get RepaintBoundary optimization statistics
  static Map<String, dynamic> getOptimizationStats() {
    // This would track how many components have been optimized
    // For now, return placeholder stats
    return {
      'optimizedComponents': 0,
      'skippedComponents': 0,
      'optimizationRatio': 0.0,
    };
  }
}

/// Validation system for side menu layout optimizations
class SideMenuOptimizationValidator {
  static final Map<String, List<Duration>> _performanceHistory = {};
  static final Map<String, int> _validationCounts = {};

  /// Validate NavigationRail performance improvements
  static Future<Map<String, dynamic>> validateNavigationRailOptimizations({
    required String railId,
    required BuildContext context,
  }) async {
    final results = <String, dynamic>{};

    try {
      // Test 1: Measure render time
      final renderTimeResult = await _measureRenderTime(railId, context);
      results['renderTime'] = renderTimeResult;

      // Test 2: Validate lazy loading efficiency
      final lazyLoadingResult = _validateLazyLoading();
      results['lazyLoading'] = lazyLoadingResult;

      // Test 3: Check component caching effectiveness
      final cachingResult = _validateComponentCaching();
      results['componentCaching'] = cachingResult;

      // Test 4: Verify RepaintBoundary optimization
      final repaintResult = _validateRepaintBoundaryOptimization();
      results['repaintBoundary'] = repaintResult;

      // Test 5: Cross-platform compatibility check
      final compatibilityResult = _validateCrossPlatformCompatibility(context);
      results['crossPlatform'] = compatibilityResult;

      // Calculate overall optimization score
      final overallScore = _calculateOptimizationScore(results);
      results['overallScore'] = overallScore;
      results['validationTime'] = DateTime.now().toIso8601String();

      // Log validation results
      if (kDebugMode) {
        _logValidationResults(railId, results);
      }
    } catch (e) {
      results['error'] = e.toString();
      AnxLog.severe('Side menu optimization validation failed: $e');
    }

    return results;
  }

  /// Measure NavigationRail render time performance
  static Future<Map<String, dynamic>> _measureRenderTime(
    String railId,
    BuildContext context,
  ) async {
    final stopwatch = Stopwatch()..start();

    // Simulate NavigationRail rebuild
    await Future<void>.delayed(const Duration(milliseconds: 1));

    stopwatch.stop();
    final renderTime = stopwatch.elapsed;

    // Store in performance history
    _performanceHistory.putIfAbsent(railId, () => <Duration>[]);
    _performanceHistory[railId]!.add(renderTime);

    // Keep only recent measurements
    if (_performanceHistory[railId]!.length > 20) {
      _performanceHistory[railId]!.removeAt(0);
    }

    // Calculate performance metrics
    final history = _performanceHistory[railId]!;
    final avgRenderTime = history.isNotEmpty
        ? history.map((d) => d.inMicroseconds).reduce((a, b) => a + b) /
            history.length
        : 0.0;

    final isOptimal = avgRenderTime < 16000; // 16ms for 60fps

    return {
      'currentRenderTime': renderTime.inMicroseconds / 1000,
      'averageRenderTime': avgRenderTime / 1000,
      'isOptimal': isOptimal,
      'targetRenderTime': 16.0,
      'measurementCount': history.length,
    };
  }

  /// Validate lazy loading efficiency
  static Map<String, dynamic> _validateLazyLoading() {
    final stats = NavigationRailLazyLoader.getCacheStats();
    final cachedCount = stats['cachedDestinations'] as int;
    final loadingCount = stats['loadingDestinations'] as int;

    final efficiency =
        cachedCount > 0 ? cachedCount / (cachedCount + loadingCount) : 0.0;
    final isEfficient = efficiency > 0.7; // 70% cache hit rate target

    return {
      'cachedDestinations': cachedCount,
      'loadingDestinations': loadingCount,
      'cacheEfficiency': efficiency,
      'isEfficient': isEfficient,
      'targetEfficiency': 0.7,
    };
  }

  /// Validate component caching effectiveness
  static Map<String, dynamic> _validateComponentCaching() {
    final stats = SideMenuComponentCache.getStats();
    final totalCached = stats['totalCached'] as int;
    final avgAccesses = stats['averageAccesses'] as double;

    final isEffective = totalCached > 0 && avgAccesses > 2.0;

    return {
      'totalCachedComponents': totalCached,
      'averageAccesses': avgAccesses,
      'isEffective': isEffective,
      'targetAccesses': 2.0,
    };
  }

  /// Validate RepaintBoundary optimization
  static Map<String, dynamic> _validateRepaintBoundaryOptimization() {
    final stats = NavigationRailRepaintOptimizer.getOptimizationStats();
    final optimizedComponents = stats['optimizedComponents'] as int;
    final optimizationRatio = stats['optimizationRatio'] as double;

    final isOptimized = optimizationRatio > 0.5; // 50% optimization target

    return {
      'optimizedComponents': optimizedComponents,
      'optimizationRatio': optimizationRatio,
      'isOptimized': isOptimized,
      'targetRatio': 0.5,
    };
  }

  /// Validate cross-platform compatibility
  static Map<String, dynamic> _validateCrossPlatformCompatibility(
    BuildContext context,
  ) {
    final isTablet = DesignSystem.isTablet(context);
    final isLandscape =
        ResponsiveSystem.getOrientation(context) == Orientation.landscape;
    final isIOS = PlatformAdaptations.isIOS;

    // Check if optimizations are properly applied for current platform
    final isCompatible = isTablet &&
        isLandscape; // Optimizations should only apply to tablet landscape

    return {
      'isTablet': isTablet,
      'isLandscape': isLandscape,
      'isIOS': isIOS,
      'isCompatible': isCompatible,
      'shouldOptimize': isTablet && isLandscape,
    };
  }

  /// Calculate overall optimization score (0-100)
  static double _calculateOptimizationScore(Map<String, dynamic> results) {
    double score = 0.0;
    int testCount = 0;

    // Render time score (25 points)
    final renderTime = results['renderTime'] as Map<String, dynamic>?;
    if (renderTime != null && renderTime['isOptimal'] == true) {
      score += 25.0;
    }
    testCount++;

    // Lazy loading score (25 points)
    final lazyLoading = results['lazyLoading'] as Map<String, dynamic>?;
    if (lazyLoading != null && lazyLoading['isEfficient'] == true) {
      score += 25.0;
    }
    testCount++;

    // Component caching score (25 points)
    final caching = results['componentCaching'] as Map<String, dynamic>?;
    if (caching != null && caching['isEffective'] == true) {
      score += 25.0;
    }
    testCount++;

    // RepaintBoundary score (25 points)
    final repaint = results['repaintBoundary'] as Map<String, dynamic>?;
    if (repaint != null && repaint['isOptimized'] == true) {
      score += 25.0;
    }
    testCount++;

    return testCount > 0 ? score : 0.0;
  }

  /// Log validation results for debugging
  static void _logValidationResults(
      String railId, Map<String, dynamic> results) {
    final score = results['overallScore'] as double;

    AnxLog.info('🔍 Side Menu Optimization Validation Results ($railId):');
    AnxLog.info('   Overall Score: ${score.toStringAsFixed(1)}/100');

    final renderTime = results['renderTime'] as Map<String, dynamic>?;
    if (renderTime != null) {
      AnxLog.info(
          '   Render Time: ${renderTime['currentRenderTime']}ms (optimal: ${renderTime['isOptimal']})');
    }

    final lazyLoading = results['lazyLoading'] as Map<String, dynamic>?;
    if (lazyLoading != null) {
      final efficiency = (lazyLoading['cacheEfficiency'] as double) * 100;
      AnxLog.info(
          '   Lazy Loading: ${efficiency.toStringAsFixed(1)}% efficiency');
    }

    final caching = results['componentCaching'] as Map<String, dynamic>?;
    if (caching != null) {
      AnxLog.info(
          '   Component Caching: ${caching['totalCachedComponents']} cached components');
    }

    final repaint = results['repaintBoundary'] as Map<String, dynamic>?;
    if (repaint != null) {
      AnxLog.info(
          '   RepaintBoundary: ${repaint['optimizedComponents']} optimized components');
    }

    if (score >= 80) {
      AnxLog.info('   ✅ Excellent optimization performance!');
    } else if (score >= 60) {
      AnxLog.info('   ⚠️ Good optimization, room for improvement');
    } else {
      AnxLog.warning('   ❌ Optimization needs attention');
    }
  }

  /// Get validation history for performance tracking
  static Map<String, dynamic> getValidationHistory(String railId) {
    final history = _performanceHistory[railId] ?? [];
    final validationCount = _validationCounts[railId] ?? 0;

    return {
      'railId': railId,
      'performanceHistory':
          history.map((d) => d.inMicroseconds / 1000).toList(),
      'validationCount': validationCount,
      'lastValidation':
          history.isNotEmpty ? history.last.inMicroseconds / 1000 : null,
    };
  }

  /// Clear validation history
  static void clearValidationHistory([String? railId]) {
    if (railId != null) {
      _performanceHistory.remove(railId);
      _validationCounts.remove(railId);
    } else {
      _performanceHistory.clear();
      _validationCounts.clear();
    }
  }
}

/// Enhanced Navigation Widgets for Dasso Reader
///
/// This file provides enhanced navigation components that use the NavigationSystem
/// for consistent, accessible, and platform-adaptive navigation.

// =====================================================
// ENHANCED TAB BAR
// =====================================================

/// Enhanced TabBar with improved visual feedback and accessibility
class EnhancedTabBar extends StatelessWidget implements PreferredSizeWidget {
  final List<NavigationDestination> destinations;
  final int currentIndex;
  final ValueChanged<int> onDestinationSelected;
  final bool isScrollable;
  final Color? backgroundColor;
  final Color? indicatorColor;

  const EnhancedTabBar({
    super.key,
    required this.destinations,
    required this.currentIndex,
    required this.onDestinationSelected,
    this.isScrollable = false,
    this.backgroundColor,
    this.indicatorColor,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Container(
      decoration: BoxDecoration(
        color: backgroundColor ?? colorScheme.surface,
        border: Border(
          bottom: BorderSide(
            color: colorScheme.outlineVariant.withAlpha(128),
            width: DesignSystem.spaceMicro,
          ),
        ),
      ),
      child: TabBar(
        isScrollable: isScrollable,
        tabs: destinations.asMap().entries.map((entry) {
          final index = entry.key;
          final destination = entry.value;
          final isSelected = index == currentIndex;

          return NavigationSystem.createNavigationTab(
            context: context,
            destination: destination,
            isSelected: isSelected,
            onTap: () => onDestinationSelected(index),
          );
        }).toList(),
        labelColor: indicatorColor ?? colorScheme.primary,
        unselectedLabelColor: colorScheme.onSurfaceVariant,
        indicatorColor: indicatorColor ?? colorScheme.primary,
        indicatorWeight: DesignSystem.spaceXS - DesignSystem.spaceMicro,
        indicatorSize: TabBarIndicatorSize.tab,
        splashBorderRadius: BorderRadius.circular(DesignSystem.radiusM),
        overlayColor: WidgetStateProperty.resolveWith<Color?>((states) {
          if (states.contains(WidgetState.hovered)) {
            return colorScheme.primary.withAlpha((0.08 * 255).round());
          }
          if (states.contains(WidgetState.pressed)) {
            return colorScheme.primary.withAlpha((0.12 * 255).round());
          }
          return null;
        }),
        onTap: (index) {
          NavigationSystem.provideFeedback(NavigationFeedbackType.selection);
          onDestinationSelected(index);
        },
      ),
    );
  }

  @override
  Size get preferredSize =>
      const Size.fromHeight(DesignSystem.spaceXXL + DesignSystem.spaceL);
}

// =====================================================
// ENHANCED NAVIGATION RAIL
// =====================================================

/// Enhanced NavigationRail with improved visual feedback and responsive behavior
class EnhancedNavigationRail extends StatefulWidget {
  final List<NavigationDestination> destinations;
  final int currentIndex;
  final ValueChanged<int> onDestinationSelected;
  final bool extended;
  final Widget? leading;
  final Widget? trailing;
  final Color? backgroundColor;

  const EnhancedNavigationRail({
    super.key,
    required this.destinations,
    required this.currentIndex,
    required this.onDestinationSelected,
    this.extended = false,
    this.leading,
    this.trailing,
    this.backgroundColor,
  });

  @override
  State<EnhancedNavigationRail> createState() => _EnhancedNavigationRailState();
}

class _EnhancedNavigationRailState extends State<EnhancedNavigationRail>
    with RebuildOptimizationMixin {
  int _rebuildCount = 0;
  final String _railId = 'enhanced_navigation_rail';

  // Track previous widget state to detect rebuild triggers
  int? _previousCurrentIndex;
  bool? _previousExtended;
  int? _previousDestinationCount;
  Widget? _previousLeading;
  Widget? _previousTrailing;

  @override
  void initState() {
    super.initState();
    // Start performance profiling
    TabletPerformanceUtils.instance.startNavigationRailProfiling(_railId);

    // Create performance baseline
    WidgetsBinding.instance.addPostFrameCallback((_) {
      TabletPerformanceUtils.instance.createNavigationRailBaseline(
        _railId,
        destinationCount: widget.destinations.length,
        hasSearchBar: widget.leading != null,
        isExtended: widget.extended,
        context: context,
      );

      // Preload common widgets for better performance
      SideMenuComponentCache.preloadCommonWidgets(context);

      // Clear expired cache entries
      SideMenuComponentCache.clearExpired();
    });
  }

  @override
  void dispose() {
    // Update baseline with final metrics
    TabletPerformanceUtils.instance.updateNavigationRailBaseline(
      _railId,
      rebuildCount: _rebuildCount,
      additionalMetrics: {
        'destinations': widget.destinations.length,
        'extended': widget.extended,
        'hasLeading': widget.leading != null,
        'hasTrailing': widget.trailing != null,
        'totalLifetime':
            DateTime.now().difference(DateTime.now()).inMilliseconds,
      },
    );

    // End performance profiling with final metrics
    TabletPerformanceUtils.instance.endNavigationRailProfiling(
      _railId,
      rebuildCount: _rebuildCount,
      additionalMetrics: {
        'destinations': widget.destinations.length,
        'extended': widget.extended,
        'hasLeading': widget.leading != null,
        'hasTrailing': widget.trailing != null,
      },
    );
    super.dispose();
  }

  /// Perform complexity analysis on the side menu
  void _performComplexityAnalysis(BuildContext context) {
    // Only perform analysis in debug mode and on tablets
    if (!kDebugMode || !DesignSystem.isTablet(context)) return;

    // Defer analysis to avoid blocking the current frame
    WidgetsBinding.instance.addPostFrameCallback((_) {
      try {
        final analysis =
            TabletPerformanceUtils.instance.analyzeSideMenuComplexity(context);

        // Log analysis results for development insights
        final complexityScore = analysis['overallComplexityScore'] as int? ?? 0;
        final recommendations =
            analysis['optimizationRecommendations'] as List<String>? ?? [];

        if (complexityScore > 60) {
          AnxLog.warning(
            '🔍 NavigationRail complexity score: $complexityScore/100',
          );
          if (recommendations.isNotEmpty) {
            AnxLog.info('💡 Optimization recommendations:');
            for (final recommendation in recommendations) {
              AnxLog.info('   • $recommendation');
            }
          }
        }
      } catch (e) {
        AnxLog.warning('🔍 NavigationRail complexity analysis failed: $e');
      }
    });
  }

  /// Detect what triggered this rebuild
  void _detectRebuildTriggers() {
    final triggers = <String>[];
    final context = <String, dynamic>{};

    // Check for navigation index changes
    if (_previousCurrentIndex != null &&
        _previousCurrentIndex != widget.currentIndex) {
      triggers.add('navigation_change');
      context['previousIndex'] = _previousCurrentIndex;
      context['newIndex'] = widget.currentIndex;
    }

    // Check for extended state changes
    if (_previousExtended != null && _previousExtended != widget.extended) {
      triggers.add('extended_change');
      context['previousExtended'] = _previousExtended;
      context['newExtended'] = widget.extended;
    }

    // Check for destination count changes
    if (_previousDestinationCount != null &&
        _previousDestinationCount != widget.destinations.length) {
      triggers.add('destinations_change');
      context['previousCount'] = _previousDestinationCount;
      context['newCount'] = widget.destinations.length;
    }

    // Check for leading widget changes (search bar, etc.)
    if (_previousLeading != widget.leading) {
      triggers.add('leading_change');
      context['hasLeading'] = widget.leading != null;
    }

    // Check for trailing widget changes
    if (_previousTrailing != widget.trailing) {
      triggers.add('trailing_change');
      context['hasTrailing'] = widget.trailing != null;
    }

    // If no specific trigger detected, it's likely a parent rebuild
    if (triggers.isEmpty && _rebuildCount > 1) {
      triggers.add('parent_rebuild');
    }

    // If this is the first rebuild, it's initialization
    if (_rebuildCount == 1) {
      triggers.add('initialization');
    }

    // Record all detected triggers
    for (final trigger in triggers) {
      TabletPerformanceUtils.instance.recordNavigationRailRebuild(
        _railId,
        trigger,
        context: context,
      );
    }

    // Update previous state for next comparison
    _updatePreviousState();
  }

  /// Update previous state tracking
  void _updatePreviousState() {
    _previousCurrentIndex = widget.currentIndex;
    _previousExtended = widget.extended;
    _previousDestinationCount = widget.destinations.length;
    _previousLeading = widget.leading;
    _previousTrailing = widget.trailing;
  }

  /// Build optimized destinations with RepaintBoundary and lazy loading
  List<NavigationRailDestination> _buildOptimizedDestinations(
    BuildContext context,
  ) {
    final destinations = <NavigationRailDestination>[];

    for (int i = 0; i < widget.destinations.length; i++) {
      final destination = widget.destinations[i];
      final key = '${_railId}_destination_$i';

      // Use lazy loader to get or create destination (caching handled internally)
      final baseDestination = NavigationRailLazyLoader.getDestination(
        key,
        destination,
        context,
        forceReload: _rebuildCount <= 1, // Force reload on first build
      );

      // Wrap destination components with RepaintBoundary for performance isolation
      final optimizedDestination = NavigationRailDestination(
        icon: RepaintBoundary(
          child: baseDestination.icon,
        ),
        selectedIcon: RepaintBoundary(
          child: baseDestination.selectedIcon,
        ),
        label: RepaintBoundary(
          child: baseDestination.label,
        ),
      );

      destinations.add(optimizedDestination);
    }

    // Log combined caching and lazy loading statistics
    if (kDebugMode && _rebuildCount % 10 == 0) {
      final lazyStats = NavigationRailLazyLoader.getCacheStats();
      final cacheStats = SideMenuComponentCache.getStats();

      AnxLog.info(
        '🔄 NavigationRail optimization stats:\n'
        '   Lazy cached destinations: ${lazyStats['cachedDestinations']}\n'
        '   Component cache hits: ${cacheStats['totalCached']}\n'
        '   Cache efficiency: ${(lazyStats['cachedDestinations'] as int) > 0 ? ((lazyStats['cachedDestinations'] as int) / widget.destinations.length * 100).toStringAsFixed(1) : '0'}%\n'
        '   Component cache efficiency: ${cacheStats['averageAccesses']}',
      );
    }

    return destinations;
  }

  @override
  Widget buildOptimized(BuildContext context) {
    _rebuildCount++;
    final colorScheme = Theme.of(context).colorScheme;

    // Detect and record rebuild triggers
    _detectRebuildTriggers();

    // Track NavigationRail metrics for performance monitoring
    TabletPerformanceUtils.instance.trackNavigationRailMetrics(
      menuItemCount: widget.destinations.length,
      hasSearchBar: widget.leading != null,
      isExpanded: widget.extended,
      activeAnimations: 0, // Will be updated when animations are detected
    );

    // Perform complexity analysis (only on first few rebuilds to avoid spam)
    if (_rebuildCount <= 3) {
      _performComplexityAnalysis(context);
    }

    return RepaintBoundary(
      child: Container(
        decoration: BoxDecoration(
          color: widget.backgroundColor ?? colorScheme.surface,
          border: Border(
            right: BorderSide(
              color: colorScheme.outlineVariant.withAlpha(128),
              width: DesignSystem.spaceMicro,
            ),
          ),
        ),
        child: NavigationRail(
          extended: widget.extended,
          selectedIndex: widget.currentIndex,
          onDestinationSelected: (index) {
            NavigationSystem.provideFeedback(NavigationFeedbackType.selection);
            widget.onDestinationSelected(index);
          },
          leading: widget.leading != null
              ? NavigationRailRepaintOptimizer.optimizeLeading(widget.leading!)
              : null,
          trailing: widget.trailing != null
              ? NavigationRailRepaintOptimizer.optimizeTrailing(
                  widget.trailing!,
                )
              : null,
          destinations: _buildOptimizedDestinations(context),
          labelType: widget.extended
              ? NavigationRailLabelType.none
              : NavigationRailLabelType.all,
          backgroundColor: Colors.transparent,
          selectedIconTheme: IconThemeData(
            color: colorScheme.primary,
            size: AppIcons.sizeM,
          ),
          unselectedIconTheme: IconThemeData(
            color: colorScheme.onSurfaceVariant,
            size: AppIcons.sizeM,
          ),
          selectedLabelTextStyle: TextStyle(
            color: colorScheme.primary,
            fontWeight: DesignSystem.getAdjustedFontWeight(FontWeight.w600),
            fontSize: DesignSystem.fontSizeS,
          ),
          unselectedLabelTextStyle: TextStyle(
            color: colorScheme.onSurfaceVariant,
            fontWeight: DesignSystem.getAdjustedFontWeight(FontWeight.w500),
            fontSize: DesignSystem.fontSizeS,
          ),
          indicatorColor: colorScheme.primaryContainer,
          indicatorShape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(DesignSystem.radiusM),
          ),
        ),
      ),
    );
  }
}

// =====================================================
// ENHANCED BOTTOM NAVIGATION BAR
// =====================================================

/// Enhanced BottomNavigationBar with improved visual feedback and adaptive height
class EnhancedBottomNavigationBar extends StatelessWidget {
  final List<NavigationDestination> destinations;
  final int currentIndex;
  final ValueChanged<int> onDestinationSelected;
  final Color? backgroundColor;
  final double? height;
  final EdgeInsetsGeometry? margin;

  const EnhancedBottomNavigationBar({
    super.key,
    required this.destinations,
    required this.currentIndex,
    required this.onDestinationSelected,
    this.backgroundColor,
    this.height,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    // Calculate adaptive height matching your TabBar implementation
    final adaptiveHeight = height ?? _calculateAdaptiveHeight(context);

    // Use NavigationBar for Material 3 design with enhanced customization
    return Container(
      height: adaptiveHeight,
      margin: margin,
      child: NavigationBar(
        selectedIndex: currentIndex,
        onDestinationSelected: (index) {
          NavigationSystem.provideFeedback(NavigationFeedbackType.selection);
          onDestinationSelected(index);
        },
        backgroundColor: backgroundColor ?? colorScheme.surface,
        indicatorColor: colorScheme.primaryContainer,
        indicatorShape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(DesignSystem.radiusM),
        ),
        destinations: destinations.map((destination) {
          return material.NavigationDestination(
            icon: Icon(
              destination.icon,
              size: AppIcons.sizeM, // Consistent with your design system
            ),
            selectedIcon: Icon(
              destination.getIcon(selected: true),
              size: AppIcons.sizeM,
            ),
            label: destination.getLabel(context),
            tooltip: destination.getTooltip(context),
          );
        }).toList(),
        labelBehavior: NavigationDestinationLabelBehavior.alwaysShow,
        animationDuration: DesignSystem.durationFast,
        // Enhanced elevation for better visual hierarchy with manufacturer adjustments
        elevation: DesignSystem.getAdjustedElevation(DesignSystem.elevationS),
      ),
    );
  }

  /// Calculate adaptive height matching the TabBar implementation
  double _calculateAdaptiveHeight(BuildContext context) {
    final screenSize = ResponsiveSystem.getScreenSize(context);
    final devicePixelRatio =
        screenSize.aspectRatio; // Use aspect ratio as proxy for density
    final textScaler = MediaQuery.textScalerOf(context);

    const baseHeight = DesignSystem.spaceXXL +
        DesignSystem.spaceL +
        DesignSystem.spaceTiny; // Match Material Design standard
    const minHeight = DesignSystem.widgetMinTouchTarget +
        DesignSystem.spaceXS; // Accessibility minimum

    // Scale for device density
    double densityFactor = (devicePixelRatio / 3.0).clamp(0.85, 1.25);

    // Scale for text size
    double textFactor = textScaler.scale(1.0).clamp(0.9, 1.3);

    // Device-specific adjustments
    double deviceFactor = 1.0;
    if (DesignSystem.isSmallPhone(context)) {
      deviceFactor = 0.9;
    } else if (DesignSystem.isTablet(context)) {
      deviceFactor = 1.1;
    }

    double calculatedHeight =
        baseHeight * densityFactor * textFactor * deviceFactor;

    // Ensure accessibility compliance
    return math.max(calculatedHeight, minHeight);
  }
}

// =====================================================
// ADAPTIVE NAVIGATION WRAPPER
// =====================================================

/// Adaptive navigation wrapper that chooses the appropriate navigation pattern
/// based on screen size and platform
class AdaptiveNavigationWrapper extends StatelessWidget {
  final List<NavigationDestination> destinations;
  final int currentIndex;
  final ValueChanged<int> onDestinationSelected;
  final Widget child;
  final Widget? leading;
  final Widget? trailing;
  final bool forceBottomNavigation;

  const AdaptiveNavigationWrapper({
    super.key,
    required this.destinations,
    required this.currentIndex,
    required this.onDestinationSelected,
    required this.child,
    this.leading,
    this.trailing,
    this.forceBottomNavigation = false,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Show side menu (NavigationRail) only for tablets in landscape orientation
        // or for desktop-sized screens (width-based fallback for desktop support)
        // Mobile devices always use bottom navigation
        final shouldUseRail = !forceBottomNavigation &&
            (constraints.maxWidth > DesignSystem.breakpointDesktop ||
                (DesignSystem.isTablet(context) &&
                    ResponsiveSystem.getOrientation(context) ==
                        Orientation.landscape));

        if (shouldUseRail) {
          // Use NavigationRail for larger screens
          final extended =
              constraints.maxWidth > DesignSystem.breakpointDesktop;

          return Scaffold(
            body: Row(
              children: [
                EnhancedNavigationRail(
                  destinations: destinations,
                  currentIndex: currentIndex,
                  onDestinationSelected: onDestinationSelected,
                  extended: extended,
                  leading: leading,
                  trailing: trailing,
                ),
                Expanded(child: child),
              ],
            ),
          );
        } else {
          // Use BottomNavigationBar for smaller screens
          return Scaffold(
            body: child,
            bottomNavigationBar: EnhancedBottomNavigationBar(
              destinations: destinations,
              currentIndex: currentIndex,
              onDestinationSelected: onDestinationSelected,
            ),
          );
        }
      },
    );
  }
}
