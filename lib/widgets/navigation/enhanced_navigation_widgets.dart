import 'dart:math' as math;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart' hide NavigationDestination;
import 'package:dasso_reader/config/navigation_system.dart';
import 'package:dasso_reader/config/design_system.dart';
import 'package:dasso_reader/config/responsive_system.dart';
import 'package:dasso_reader/config/app_icons.dart';
import 'package:dasso_reader/utils/performance/tablet_performance_utils.dart';
import 'package:dasso_reader/utils/state_management/rebuild_optimization.dart';
import 'package:dasso_reader/utils/log/common.dart';
import 'package:flutter/material.dart' as material;

/// Enhanced Navigation Widgets for Dasso Reader
///
/// This file provides enhanced navigation components that use the NavigationSystem
/// for consistent, accessible, and platform-adaptive navigation.

// =====================================================
// ENHANCED TAB BAR
// =====================================================

/// Enhanced TabBar with improved visual feedback and accessibility
class EnhancedTabBar extends StatelessWidget implements PreferredSizeWidget {
  final List<NavigationDestination> destinations;
  final int currentIndex;
  final ValueChanged<int> onDestinationSelected;
  final bool isScrollable;
  final Color? backgroundColor;
  final Color? indicatorColor;

  const EnhancedTabBar({
    super.key,
    required this.destinations,
    required this.currentIndex,
    required this.onDestinationSelected,
    this.isScrollable = false,
    this.backgroundColor,
    this.indicatorColor,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Container(
      decoration: BoxDecoration(
        color: backgroundColor ?? colorScheme.surface,
        border: Border(
          bottom: BorderSide(
            color: colorScheme.outlineVariant.withAlpha(128),
            width: DesignSystem.spaceMicro,
          ),
        ),
      ),
      child: TabBar(
        isScrollable: isScrollable,
        tabs: destinations.asMap().entries.map((entry) {
          final index = entry.key;
          final destination = entry.value;
          final isSelected = index == currentIndex;

          return NavigationSystem.createNavigationTab(
            context: context,
            destination: destination,
            isSelected: isSelected,
            onTap: () => onDestinationSelected(index),
          );
        }).toList(),
        labelColor: indicatorColor ?? colorScheme.primary,
        unselectedLabelColor: colorScheme.onSurfaceVariant,
        indicatorColor: indicatorColor ?? colorScheme.primary,
        indicatorWeight: DesignSystem.spaceXS - DesignSystem.spaceMicro,
        indicatorSize: TabBarIndicatorSize.tab,
        splashBorderRadius: BorderRadius.circular(DesignSystem.radiusM),
        overlayColor: WidgetStateProperty.resolveWith<Color?>((states) {
          if (states.contains(WidgetState.hovered)) {
            return colorScheme.primary.withAlpha((0.08 * 255).round());
          }
          if (states.contains(WidgetState.pressed)) {
            return colorScheme.primary.withAlpha((0.12 * 255).round());
          }
          return null;
        }),
        onTap: (index) {
          NavigationSystem.provideFeedback(NavigationFeedbackType.selection);
          onDestinationSelected(index);
        },
      ),
    );
  }

  @override
  Size get preferredSize =>
      const Size.fromHeight(DesignSystem.spaceXXL + DesignSystem.spaceL);
}

// =====================================================
// ENHANCED NAVIGATION RAIL
// =====================================================

/// Enhanced NavigationRail with improved visual feedback and responsive behavior
class EnhancedNavigationRail extends StatefulWidget {
  final List<NavigationDestination> destinations;
  final int currentIndex;
  final ValueChanged<int> onDestinationSelected;
  final bool extended;
  final Widget? leading;
  final Widget? trailing;
  final Color? backgroundColor;

  const EnhancedNavigationRail({
    super.key,
    required this.destinations,
    required this.currentIndex,
    required this.onDestinationSelected,
    this.extended = false,
    this.leading,
    this.trailing,
    this.backgroundColor,
  });

  @override
  State<EnhancedNavigationRail> createState() => _EnhancedNavigationRailState();
}

class _EnhancedNavigationRailState extends State<EnhancedNavigationRail>
    with RebuildOptimizationMixin {
  int _rebuildCount = 0;
  final String _railId = 'enhanced_navigation_rail';

  // Track previous widget state to detect rebuild triggers
  int? _previousCurrentIndex;
  bool? _previousExtended;
  int? _previousDestinationCount;
  Widget? _previousLeading;
  Widget? _previousTrailing;

  @override
  void initState() {
    super.initState();
    // Start performance profiling
    TabletPerformanceUtils.instance.startNavigationRailProfiling(_railId);
  }

  @override
  void dispose() {
    // End performance profiling with final metrics
    TabletPerformanceUtils.instance.endNavigationRailProfiling(
      _railId,
      rebuildCount: _rebuildCount,
      additionalMetrics: {
        'destinations': widget.destinations.length,
        'extended': widget.extended,
        'hasLeading': widget.leading != null,
        'hasTrailing': widget.trailing != null,
      },
    );
    super.dispose();
  }

  /// Perform complexity analysis on the side menu
  void _performComplexityAnalysis(BuildContext context) {
    // Only perform analysis in debug mode and on tablets
    if (!kDebugMode || !DesignSystem.isTablet(context)) return;

    // Defer analysis to avoid blocking the current frame
    WidgetsBinding.instance.addPostFrameCallback((_) {
      try {
        final analysis =
            TabletPerformanceUtils.instance.analyzeSideMenuComplexity(context);

        // Log analysis results for development insights
        final complexityScore = analysis['overallComplexityScore'] as int? ?? 0;
        final recommendations =
            analysis['optimizationRecommendations'] as List<String>? ?? [];

        if (complexityScore > 60) {
          AnxLog.warning(
            '🔍 NavigationRail complexity score: $complexityScore/100',
          );
          if (recommendations.isNotEmpty) {
            AnxLog.info('💡 Optimization recommendations:');
            for (final recommendation in recommendations) {
              AnxLog.info('   • $recommendation');
            }
          }
        }
      } catch (e) {
        AnxLog.warning('🔍 NavigationRail complexity analysis failed: $e');
      }
    });
  }

  /// Detect what triggered this rebuild
  void _detectRebuildTriggers() {
    final triggers = <String>[];
    final context = <String, dynamic>{};

    // Check for navigation index changes
    if (_previousCurrentIndex != null &&
        _previousCurrentIndex != widget.currentIndex) {
      triggers.add('navigation_change');
      context['previousIndex'] = _previousCurrentIndex;
      context['newIndex'] = widget.currentIndex;
    }

    // Check for extended state changes
    if (_previousExtended != null && _previousExtended != widget.extended) {
      triggers.add('extended_change');
      context['previousExtended'] = _previousExtended;
      context['newExtended'] = widget.extended;
    }

    // Check for destination count changes
    if (_previousDestinationCount != null &&
        _previousDestinationCount != widget.destinations.length) {
      triggers.add('destinations_change');
      context['previousCount'] = _previousDestinationCount;
      context['newCount'] = widget.destinations.length;
    }

    // Check for leading widget changes (search bar, etc.)
    if (_previousLeading != widget.leading) {
      triggers.add('leading_change');
      context['hasLeading'] = widget.leading != null;
    }

    // Check for trailing widget changes
    if (_previousTrailing != widget.trailing) {
      triggers.add('trailing_change');
      context['hasTrailing'] = widget.trailing != null;
    }

    // If no specific trigger detected, it's likely a parent rebuild
    if (triggers.isEmpty && _rebuildCount > 1) {
      triggers.add('parent_rebuild');
    }

    // If this is the first rebuild, it's initialization
    if (_rebuildCount == 1) {
      triggers.add('initialization');
    }

    // Record all detected triggers
    for (final trigger in triggers) {
      TabletPerformanceUtils.instance.recordNavigationRailRebuild(
        _railId,
        trigger,
        context: context,
      );
    }

    // Update previous state for next comparison
    _updatePreviousState();
  }

  /// Update previous state tracking
  void _updatePreviousState() {
    _previousCurrentIndex = widget.currentIndex;
    _previousExtended = widget.extended;
    _previousDestinationCount = widget.destinations.length;
    _previousLeading = widget.leading;
    _previousTrailing = widget.trailing;
  }

  @override
  Widget buildOptimized(BuildContext context) {
    _rebuildCount++;
    final colorScheme = Theme.of(context).colorScheme;

    // Detect and record rebuild triggers
    _detectRebuildTriggers();

    // Track NavigationRail metrics for performance monitoring
    TabletPerformanceUtils.instance.trackNavigationRailMetrics(
      menuItemCount: widget.destinations.length,
      hasSearchBar: widget.leading != null,
      isExpanded: widget.extended,
      activeAnimations: 0, // Will be updated when animations are detected
    );

    // Perform complexity analysis (only on first few rebuilds to avoid spam)
    if (_rebuildCount <= 3) {
      _performComplexityAnalysis(context);
    }

    return RepaintBoundary(
      child: Container(
        decoration: BoxDecoration(
          color: widget.backgroundColor ?? colorScheme.surface,
          border: Border(
            right: BorderSide(
              color: colorScheme.outlineVariant.withAlpha(128),
              width: DesignSystem.spaceMicro,
            ),
          ),
        ),
        child: NavigationRail(
          extended: widget.extended,
          selectedIndex: widget.currentIndex,
          onDestinationSelected: (index) {
            NavigationSystem.provideFeedback(NavigationFeedbackType.selection);
            widget.onDestinationSelected(index);
          },
          leading: widget.leading,
          trailing: widget.trailing,
          destinations: widget.destinations.map((destination) {
            return NavigationRailDestination(
              icon: Icon(destination.icon),
              selectedIcon: Icon(destination.getIcon(selected: true)),
              label: Text(destination.getLabel(context)),
            );
          }).toList(),
          labelType: widget.extended
              ? NavigationRailLabelType.none
              : NavigationRailLabelType.all,
          backgroundColor: Colors.transparent,
          selectedIconTheme: IconThemeData(
            color: colorScheme.primary,
            size: AppIcons.sizeM,
          ),
          unselectedIconTheme: IconThemeData(
            color: colorScheme.onSurfaceVariant,
            size: AppIcons.sizeM,
          ),
          selectedLabelTextStyle: TextStyle(
            color: colorScheme.primary,
            fontWeight: DesignSystem.getAdjustedFontWeight(FontWeight.w600),
            fontSize: DesignSystem.fontSizeS,
          ),
          unselectedLabelTextStyle: TextStyle(
            color: colorScheme.onSurfaceVariant,
            fontWeight: DesignSystem.getAdjustedFontWeight(FontWeight.w500),
            fontSize: DesignSystem.fontSizeS,
          ),
          indicatorColor: colorScheme.primaryContainer,
          indicatorShape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(DesignSystem.radiusM),
          ),
        ),
      ),
    );
  }
}

// =====================================================
// ENHANCED BOTTOM NAVIGATION BAR
// =====================================================

/// Enhanced BottomNavigationBar with improved visual feedback and adaptive height
class EnhancedBottomNavigationBar extends StatelessWidget {
  final List<NavigationDestination> destinations;
  final int currentIndex;
  final ValueChanged<int> onDestinationSelected;
  final Color? backgroundColor;
  final double? height;
  final EdgeInsetsGeometry? margin;

  const EnhancedBottomNavigationBar({
    super.key,
    required this.destinations,
    required this.currentIndex,
    required this.onDestinationSelected,
    this.backgroundColor,
    this.height,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    // Calculate adaptive height matching your TabBar implementation
    final adaptiveHeight = height ?? _calculateAdaptiveHeight(context);

    // Use NavigationBar for Material 3 design with enhanced customization
    return Container(
      height: adaptiveHeight,
      margin: margin,
      child: NavigationBar(
        selectedIndex: currentIndex,
        onDestinationSelected: (index) {
          NavigationSystem.provideFeedback(NavigationFeedbackType.selection);
          onDestinationSelected(index);
        },
        backgroundColor: backgroundColor ?? colorScheme.surface,
        indicatorColor: colorScheme.primaryContainer,
        indicatorShape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(DesignSystem.radiusM),
        ),
        destinations: destinations.map((destination) {
          return material.NavigationDestination(
            icon: Icon(
              destination.icon,
              size: AppIcons.sizeM, // Consistent with your design system
            ),
            selectedIcon: Icon(
              destination.getIcon(selected: true),
              size: AppIcons.sizeM,
            ),
            label: destination.getLabel(context),
            tooltip: destination.getTooltip(context),
          );
        }).toList(),
        labelBehavior: NavigationDestinationLabelBehavior.alwaysShow,
        animationDuration: DesignSystem.durationFast,
        // Enhanced elevation for better visual hierarchy with manufacturer adjustments
        elevation: DesignSystem.getAdjustedElevation(DesignSystem.elevationS),
      ),
    );
  }

  /// Calculate adaptive height matching the TabBar implementation
  double _calculateAdaptiveHeight(BuildContext context) {
    final screenSize = ResponsiveSystem.getScreenSize(context);
    final devicePixelRatio =
        screenSize.aspectRatio; // Use aspect ratio as proxy for density
    final textScaler = MediaQuery.textScalerOf(context);

    const baseHeight = DesignSystem.spaceXXL +
        DesignSystem.spaceL +
        DesignSystem.spaceTiny; // Match Material Design standard
    const minHeight = DesignSystem.widgetMinTouchTarget +
        DesignSystem.spaceXS; // Accessibility minimum

    // Scale for device density
    double densityFactor = (devicePixelRatio / 3.0).clamp(0.85, 1.25);

    // Scale for text size
    double textFactor = textScaler.scale(1.0).clamp(0.9, 1.3);

    // Device-specific adjustments
    double deviceFactor = 1.0;
    if (DesignSystem.isSmallPhone(context)) {
      deviceFactor = 0.9;
    } else if (DesignSystem.isTablet(context)) {
      deviceFactor = 1.1;
    }

    double calculatedHeight =
        baseHeight * densityFactor * textFactor * deviceFactor;

    // Ensure accessibility compliance
    return math.max(calculatedHeight, minHeight);
  }
}

// =====================================================
// ADAPTIVE NAVIGATION WRAPPER
// =====================================================

/// Adaptive navigation wrapper that chooses the appropriate navigation pattern
/// based on screen size and platform
class AdaptiveNavigationWrapper extends StatelessWidget {
  final List<NavigationDestination> destinations;
  final int currentIndex;
  final ValueChanged<int> onDestinationSelected;
  final Widget child;
  final Widget? leading;
  final Widget? trailing;
  final bool forceBottomNavigation;

  const AdaptiveNavigationWrapper({
    super.key,
    required this.destinations,
    required this.currentIndex,
    required this.onDestinationSelected,
    required this.child,
    this.leading,
    this.trailing,
    this.forceBottomNavigation = false,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Show side menu (NavigationRail) only for tablets in landscape orientation
        // or for desktop-sized screens (width-based fallback for desktop support)
        // Mobile devices always use bottom navigation
        final shouldUseRail = !forceBottomNavigation &&
            (constraints.maxWidth > DesignSystem.breakpointDesktop ||
                (DesignSystem.isTablet(context) &&
                    ResponsiveSystem.getOrientation(context) ==
                        Orientation.landscape));

        if (shouldUseRail) {
          // Use NavigationRail for larger screens
          final extended =
              constraints.maxWidth > DesignSystem.breakpointDesktop;

          return Scaffold(
            body: Row(
              children: [
                EnhancedNavigationRail(
                  destinations: destinations,
                  currentIndex: currentIndex,
                  onDestinationSelected: onDestinationSelected,
                  extended: extended,
                  leading: leading,
                  trailing: trailing,
                ),
                Expanded(child: child),
              ],
            ),
          );
        } else {
          // Use BottomNavigationBar for smaller screens
          return Scaffold(
            body: child,
            bottomNavigationBar: EnhancedBottomNavigationBar(
              destinations: destinations,
              currentIndex: currentIndex,
              onDestinationSelected: onDestinationSelected,
            ),
          );
        }
      },
    );
  }
}
